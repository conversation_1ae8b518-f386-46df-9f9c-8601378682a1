:root {
  --bg:#07070a;           /* deep space */
  --panel:#0c0f16;        /* panels */
  --text:#eaeaf2;         /* base text */
  --muted:#9aa0a6;        /* muted text */
  --flame:#ff6b35;        /* phoenix flame */
  --glow:#ffd56b;         /* soft glow */
  --accent:#6c63ff;       /* cosmic purple */
  --grid: 1200px;
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  #starfield, .space-dust, #phoenixParticleCanvas {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --bg: #000000;
    --panel: #1a1a1a;
    --text: #ffffff;
    --muted: #cccccc;
    --flame: #ff8800;
    --glow: #ffff00;
    --accent: #8888ff;
  }
}

/* Loading states */
.loading {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Focus styles for accessibility */
.btn:focus, input:focus, textarea:focus, [tabindex]:focus {
  outline: 2px solid var(--glow);
  outline-offset: 2px;
}

/* Skip link for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--panel);
  color: var(--text);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

html, body {
  height: 100%;
  background: radial-gradient(1200px 800px at 70% -10%, rgba(108,99,255,.15), transparent 60%),
              radial-gradient(800px 600px at 10% 10%, rgba(255,107,53,.12), transparent 60%),
              var(--bg);
  color: var(--text);
  margin: 0;
  font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Inter, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji";
  overflow-x: hidden;
}

a { color: var(--glow); text-decoration: none; }
a:hover { text-decoration: underline; }

/* Global layout */
header {
  position: fixed; inset-inline: 0; top: 0; z-index: 50;
  backdrop-filter: saturate(120%) blur(10px);
  background: linear-gradient(to bottom, rgba(7,7,10,.75), transparent);
  border-bottom: 1px solid rgba(255,255,255,.06);
}

.nav {
  max-width: var(--grid); margin: 0 auto; padding: .6rem 1rem; display:flex; align-items:center; justify-content:space-between;
}

.brand { display:flex; gap:.6rem; align-items:center; font-weight:700; letter-spacing:.3px; }
.dot { width: 10px; height: 10px; border-radius: 9999px; background: linear-gradient(180deg, var(--glow), var(--flame)); box-shadow: 0 0 12px var(--glow); }
.menu { display:flex; gap:1rem; font-size:.95rem; }
.menu a { opacity:.9 }

main { position: relative; }

/* Starfield canvas */
#starfield { position: fixed; inset:0; z-index:-2; display:block; }

/* Subtle dust overlay for depth */
.space-dust {
  position: fixed; inset:0; pointer-events:none; z-index:-1;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><filter id="f"><feTurbulence baseFrequency="0.9" numOctaves="2" type="fractalNoise"/></filter><rect width="200" height="200" opacity="0.03" filter="url(%23f)"/></svg>') repeat;
  mix-blend-mode: screen;
}

/* Sections */
section { padding: 7rem 1rem; }
.wrap { max-width: var(--grid); margin: 0 auto; }

/* HERO */
.hero { display:grid; min-height: 100dvh; place-items:center; position: relative; }
.hero-inner { display:grid; grid-template-columns: 1.2fr 1fr; gap: 3rem; align-items:center; width: 100%; }
.hero p.kicker { color: var(--muted); text-transform: uppercase; font-size: .8rem; letter-spacing: .2em; margin: 0 0 .6rem; }
.title { font-size: clamp(2.2rem, 5vw, 4rem); line-height: 1.05; margin: 0 0 1rem; font-weight: 800; }
.subtitle { color: var(--muted); font-size: clamp(1rem, 2vw, 1.2rem); max-width: 60ch; }

.cta { margin-top: 1.5rem; display:flex; gap:.8rem; flex-wrap: wrap; }
.btn { border:1px solid rgba(255,255,255,.12); background: rgba(255,255,255,.03); color: var(--text); padding:.8rem 1rem; border-radius: 16px; font-weight:600; cursor:pointer; }
.btn.primary { background: linear-gradient(180deg, rgba(255,213,107,.22), rgba(255,107,53,.22)); border-color: rgba(255,213,107,.45); box-shadow: 0 6px 30px rgba(255,213,107,.18); }
.btn:hover { transform: translateY(-2px); }

.phoenix-wrap { position: relative; }
.phoenix-glow { position:absolute; inset: -10%; filter: blur(40px); opacity:.35; background: radial-gradient(closest-side, var(--glow), transparent 65%); border-radius: 50%; }
svg#phoenix { width: min(480px, 38vw); display:block; transform-origin: 50% 50%; }

/* ABOUT */
.panel { background: linear-gradient(180deg, rgba(255,255,255,.02), rgba(255,255,255,.00)); border:1px solid rgba(255,255,255,.06); border-radius: 20px; padding: 1.2rem; }
.about { display:grid; gap: 1.2rem; grid-template-columns: 1.2fr .8fr; align-items: stretch; }
.terminal { background: #0a0d13; border-radius: 14px; border:1px solid rgba(255,255,255,.06); position: relative; overflow:hidden; }
.terminal .bar { display:flex; gap:.5rem; align-items:center; padding:.6rem .8rem; border-bottom:1px solid rgba(255,255,255,.06); background: linear-gradient(180deg, rgba(255,255,255,.04), transparent); }
.status-dot { width:10px; height:10px; border-radius:9999px; background:#ff5f56; box-shadow: 0 0 8px rgba(255,95,86,.8); }
.status-dot.yellow { background:#ffbd2e; box-shadow:0 0 8px rgba(255,189,46,.8); }
.status-dot.green { background:#27c93f; box-shadow:0 0 8px rgba(39,201,63,.8); }
.terminal pre { margin: 0; padding: 1rem; font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, "Liberation Mono", monospace; color: #c8e1ff; font-size: .95rem; min-height: 220px; white-space: pre-wrap; }

/* ENHANCED PROJECTS */
.projects-grid { display:grid; grid-template-columns: repeat(3, 1fr); gap: 1.5rem; perspective: 1000px; }
.card {
  position:relative; overflow:hidden; border-radius: 20px; background: var(--panel);
  border:1px solid rgba(255,255,255,.08); padding:1.5rem; min-height: 280px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
  cursor: pointer;
}
.card:hover {
  transform: translateY(-8px) rotateX(5deg) rotateY(5deg);
  box-shadow: 0 20px 60px rgba(0,0,0,.4), 0 0 0 1px rgba(255,213,107,.2);
  border-color: rgba(255,213,107,.3);
}
.card h3 { margin:.4rem 0 .6rem; font-size: 1.3rem; }
.card p { color: var(--muted); line-height: 1.6; }
.card .shine {
  position:absolute; inset:-30%;
  background: conic-gradient(from 0deg, rgba(255,213,107,.12), transparent 25%, transparent 75%, rgba(255,213,107,.12));
  transform: rotate(0deg); opacity:.6; pointer-events:none;
  transition: opacity 0.3s ease;
}
.card:hover .shine { opacity: 1; }

.card .tech-stack {
  display: flex; flex-wrap: wrap; gap: 0.4rem; margin-top: 1rem;
}
.card .tech-badge {
  background: rgba(255,255,255,.06); border: 1px solid rgba(255,255,255,.1);
  padding: 0.3rem 0.6rem; border-radius: 12px; font-size: 0.8rem;
  color: var(--glow); transition: all 0.3s ease;
}
.card:hover .tech-badge {
  background: rgba(255,213,107,.1); border-color: rgba(255,213,107,.3);
  transform: translateY(-2px);
}

.card .project-icon {
  position: absolute; top: 1rem; right: 1rem;
  width: 40px; height: 40px; border-radius: 50%;
  background: linear-gradient(135deg, var(--glow), var(--flame));
  display: flex; align-items: center; justify-content: center;
  font-size: 1.2rem; opacity: 0.8;
  transition: all 0.3s ease;
}
.card:hover .project-icon {
  transform: rotate(360deg) scale(1.1);
  opacity: 1;
}

/* SKILLS */
.skills { display:grid; grid-template-columns: .9fr 1.1fr; gap: 2rem; align-items:center; }
.skill-cloud { position: relative; aspect-ratio: 1/1; }
.skill-cloud svg { width: 100%; height: auto; display:block; }
.badge { display:inline-flex; gap:.5rem; align-items:center; background: rgba(255,255,255,.04); border:1px solid rgba(255,255,255,.08); padding:.5rem .7rem; border-radius: 9999px; margin: .25rem; font-size: .95rem; }

/* CONTACT */
form { display:grid; gap: .9rem; }
label { font-size:.9rem; color: var(--muted); }
input, textarea { width:100%; background: rgba(255,255,255,.03); border:1px solid rgba(255,255,255,.1); color: var(--text); border-radius: 12px; padding:.8rem; outline: none; }
input:focus, textarea:focus { border-color: var(--glow); box-shadow: 0 0 0 3px rgba(255,213,107,.15); }
textarea { min-height: 140px; }

footer { padding: 2rem 1rem 3rem; color: var(--muted); text-align:center; }

/* Utils */
.muted { color: var(--muted); }
.grid-2 { display:grid; grid-template-columns: 1fr 1fr; gap: 1rem; }

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  :root { --grid: 95%; }
  .projects-grid { grid-template-columns: repeat(2, 1fr); gap: 1.2rem; }
}

@media (max-width: 1000px) {
  .hero-inner { grid-template-columns: 1fr; text-align:center; gap: 2rem; }
  .phoenix-wrap { order:-1; }
  .about { grid-template-columns: 1fr; }
  .skills { grid-template-columns: 1fr; text-align: center; }
  .skill-cloud { max-width: 300px; margin: 0 auto; }

  /* Reduce motion on tablets */
  .card:hover { transform: translateY(-4px); }
}

@media (max-width: 768px) {
  section { padding: 4rem 1rem; }
  .title { font-size: clamp(1.8rem, 6vw, 2.5rem); }
  .projects-grid { grid-template-columns: 1fr; gap: 1rem; }
  .card { padding: 1.2rem; min-height: 240px; }
  .cta { flex-direction: column; align-items: center; }
  .btn { width: 100%; max-width: 280px; text-align: center; }

  /* Mobile navigation improvements */
  .nav { padding: 0.8rem 1rem; }
  .menu { gap: 0.8rem; font-size: 0.9rem; }

  /* Touch-friendly sizing */
  .card .tech-badge { padding: 0.4rem 0.8rem; }
  input, textarea { padding: 1rem; font-size: 16px; } /* Prevent zoom on iOS */
}

@media (max-width: 480px) {
  .hero { min-height: 90vh; }
  .title { font-size: 2rem; line-height: 1.1; }
  .subtitle { font-size: 1rem; }
  section { padding: 3rem 0.8rem; }
  .card { padding: 1rem; min-height: 200px; }
  .nav { flex-direction: column; gap: 1rem; text-align: center; }
  .menu { justify-content: center; flex-wrap: wrap; }

  /* Simplified animations for mobile */
  .card:hover { transform: none; box-shadow: 0 8px 25px rgba(0,0,0,.3); }
  .phoenix-wrap { max-width: 280px; margin: 0 auto; }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .card:hover { transform: none; }
  .card:active { transform: scale(0.98); }
  .btn:hover { transform: none; }
  .btn:active { transform: scale(0.95); }

  /* Disable complex animations on touch devices */
  .card .shine { display: none; }
  #phoenixParticleCanvas { display: none; }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .dot { box-shadow: 0 0 8px var(--glow); }
  .status-dot { box-shadow: 0 0 6px rgba(255,95,86,.6); }
}

/* Landscape mobile orientation */
@media (max-height: 500px) and (orientation: landscape) {
  .hero { min-height: 100vh; padding: 2rem 1rem; }
  .hero-inner { gap: 1.5rem; }
  section { padding: 3rem 1rem; }
}

/* Screen reader only class */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Custom Cursor Effects */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: rgba(255, 213, 107, 0.8);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
  transform: translate(-50%, -50%);
}

.custom-cursor-follower {
  position: fixed;
  width: 40px;
  height: 40px;
  border: 2px solid rgba(255, 213, 107, 0.3);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.cursor-hover .custom-cursor {
  transform: translate(-50%, -50%) scale(1.5);
  background: rgba(255, 107, 53, 0.9);
}

.cursor-hover .custom-cursor-follower {
  transform: translate(-50%, -50%) scale(1.5);
  border-color: rgba(255, 107, 53, 0.5);
}

.cursor-text .custom-cursor {
  transform: translate(-50%, -50%) scale(0.5);
}

.cursor-text .custom-cursor-follower {
  transform: translate(-50%, -50%) scale(2);
  border-color: rgba(108, 99, 255, 0.5);
}

/* Hide default cursor on interactive elements */
body.custom-cursor-enabled {
  cursor: none;
}

body.custom-cursor-enabled a,
body.custom-cursor-enabled button,
body.custom-cursor-enabled .btn,
body.custom-cursor-enabled .card {
  cursor: none;
}

/* Scroll Progress Indicator */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, var(--glow), var(--flame), var(--accent));
  z-index: 1000;
  transition: width 0.1s ease;
}

/* Enhanced Hover Effects */
.enhanced-hover {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.enhanced-hover::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 213, 107, 0.1) 0%, transparent 70%);
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
}

.enhanced-hover:hover::before {
  width: 200%;
  height: 200%;
}

.enhanced-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(255, 213, 107, 0.2);
}

/* Parallax Elements */
.parallax-element {
  transition: transform 0.1s ease-out;
}

.parallax-slow {
  transition: transform 0.2s ease-out;
}

.parallax-fast {
  transition: transform 0.05s ease-out;
}

/* Mobile and Touch Device Optimizations */
@media (max-width: 768px) {
  .custom-cursor,
  .custom-cursor-follower {
    display: none !important;
  }

  body.custom-cursor-enabled {
    cursor: auto;
  }

  body.custom-cursor-enabled a,
  body.custom-cursor-enabled button,
  body.custom-cursor-enabled .btn,
  body.custom-cursor-enabled .card {
    cursor: pointer;
  }

  .parallax-element {
    transform: none !important;
  }

  .enhanced-hover:hover {
    transform: none;
  }

  .enhanced-hover:active {
    transform: scale(0.98);
  }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  .custom-cursor,
  .custom-cursor-follower,
  .scroll-progress {
    display: none !important;
  }

  .parallax-element,
  .enhanced-hover,
  #phoenix,
  .phoenix-wrap {
    transform: none !important;
    transition: none !important;
  }

  .enhanced-hover::before {
    display: none;
  }
}

/* Performance Optimizations */
.parallax-element,
.enhanced-hover,
#phoenix,
.phoenix-wrap {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth Scroll Enhancement */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

/* Loading Animation for Enhanced Elements */
.enhanced-hover,
.parallax-element {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (prefers-reduced-motion: reduce) {
  .enhanced-hover,
  .parallax-element {
    animation: none;
    opacity: 1;
    transform: none;
  }
}
