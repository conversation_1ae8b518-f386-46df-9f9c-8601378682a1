// Fallback smooth scroll implementation
class FallbackLenis {
  constructor(options = {}) {
    this.lerp = options.lerp || 0.1;
    this.wheelMultiplier = options.wheelMultiplier || 1.2;
    this.callbacks = [];
    this.targetScroll = window.pageYOffset;
    this.currentScroll = window.pageYOffset;
    this.init();
  }

  init() {
    this.bindEvents();
    this.raf();
  }

  bindEvents() {
    // Only prevent default on desktop, allow normal scroll on mobile
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (!isMobile) {
      window.addEventListener('wheel', (e) => {
        e.preventDefault();
        this.targetScroll += e.deltaY * this.wheelMultiplier;
        this.targetScroll = Math.max(0, Math.min(this.targetScroll, document.body.scrollHeight - window.innerHeight));
      }, { passive: false });
    }

    // Always allow keyboard navigation
    window.addEventListener('keydown', (e) => {
      switch(e.key) {
        case 'ArrowDown':
        case 'PageDown':
          this.targetScroll += 100;
          break;
        case 'ArrowUp':
        case 'PageUp':
          this.targetScroll -= 100;
          break;
        case 'Home':
          this.targetScroll = 0;
          break;
        case 'End':
          this.targetScroll = document.body.scrollHeight - window.innerHeight;
          break;
      }
      this.targetScroll = Math.max(0, Math.min(this.targetScroll, document.body.scrollHeight - window.innerHeight));
    });
  }

  raf() {
    this.currentScroll += (this.targetScroll - this.currentScroll) * this.lerp;
    window.scrollTo(0, this.currentScroll);

    this.callbacks.forEach(callback => {
      callback({ scroll: this.currentScroll });
    });

    requestAnimationFrame(() => this.raf());
  }

  on(event, callback) {
    if (event === 'scroll') {
      this.callbacks.push(callback);
    }
  }

  scrollTo(target, options = {}) {
    const element = typeof target === 'string' ? document.querySelector(target) : target;
    if (element) {
      const offset = options.offset || 0;
      this.targetScroll = element.offsetTop + offset;
    }
  }

  raf(time) {
    // This method is called by the main RAF loop
    // The actual animation is handled in the internal raf() method
  }
}

// Initialize libraries with error handling
let lenis, gsap, ScrollTrigger, anime;
window.portfolioInitialized = false;

// Check if libraries loaded successfully
const initializeLibraries = () => {
  if (window.portfolioInitialized) return;

  try {
    let librariesLoaded = true;

    // Initialize GSAP
    if (typeof window.gsap !== 'undefined') {
      gsap = window.gsap;
      if (typeof window.ScrollTrigger !== 'undefined') {
        ScrollTrigger = window.ScrollTrigger;
        gsap.registerPlugin(ScrollTrigger);
      }
      console.log('✅ GSAP loaded successfully');
    } else {
      librariesLoaded = false;
    }

    // Initialize Anime.js
    if (typeof window.anime !== 'undefined') {
      anime = window.anime;
      console.log('✅ Anime.js loaded successfully');
    } else {
      librariesLoaded = false;
    }

    // Initialize Lenis for buttery smooth scrolling
    if (typeof window.Lenis !== 'undefined') {
      lenis = new window.Lenis({
        duration: 1.2,
        easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
        direction: 'vertical',
        gestureDirection: 'vertical',
        smooth: true,
        mouseMultiplier: 1,
        smoothTouch: false,
        touchMultiplier: 2,
        infinite: false,
      });

      // Start the Lenis animation loop
      function raf(time) {
        lenis.raf(time);
        requestAnimationFrame(raf);
      }
      requestAnimationFrame(raf);

      console.log('✅ Lenis loaded successfully');
    } else {
      // Fallback smooth scroll
      console.log('✅ Using native smooth scroll fallback');
      lenis = {
        on: (event, callback) => {
          if (event === 'scroll') {
            window.addEventListener('scroll', () => {
              callback({ scroll: window.pageYOffset });
            }, { passive: true });
          }
        },
        scrollTo: (target, options = {}) => {
          const element = typeof target === 'string' ? document.querySelector(target) : target;
          if (element) {
            element.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        },
        raf: () => {} // No-op for compatibility
      };
    }

    if (librariesLoaded) {
      console.log('✅ All core libraries loaded, starting full experience');
    } else {
      console.log('✅ Using enhanced fallback mode for maximum compatibility');
    }

    // Start the application
    window.portfolioInitialized = true;
    initializeApp();

  } catch (error) {
    console.error('Library initialization error:', error);
    // Fallback initialization
    if (!window.portfolioInitialized) {
      initializeFallback();
    }
  }
};

// Fallback initialization without external libraries
const initializeFallback = () => {
  if (window.portfolioInitialized) return;

  console.log('🔄 Running in fallback mode');
  window.portfolioInitialized = true;

  // Basic smooth scroll fallback
  lenis = new FallbackLenis({
    lerp: 0.1,
    wheelMultiplier: 1.2
  });

  // Basic animation fallbacks
  gsap = {
    to: (target, options) => {
      const elements = typeof target === 'string' ? document.querySelectorAll(target) : [target];
      elements.forEach(el => {
        if (el && el.style) {
          Object.keys(options).forEach(prop => {
            if (prop !== 'duration' && prop !== 'ease' && prop !== 'delay' && prop !== 'onComplete') {
              if (prop === 'y') {
                el.style.transform = `translateY(${options[prop]}px)`;
              } else if (prop === 'opacity') {
                el.style.opacity = options[prop];
              } else if (prop === 'scale') {
                el.style.transform = `scale(${options[prop]})`;
              }
            }
          });
        }
      });
      // Call onComplete if provided
      if (options.onComplete) {
        setTimeout(options.onComplete, options.duration || 0);
      }
    },
    from: (target, options) => {
      // For 'from' animations, we'll just apply the end state immediately
      gsap.to(target, { ...options, duration: 0 });
    },
    timeline: () => ({
      to: gsap.to,
      from: gsap.from,
      call: (fn) => fn()
    }),
    utils: {
      toArray: (selector) => Array.from(document.querySelectorAll(selector))
    },
    registerPlugin: () => {} // No-op for fallback
  };

  anime = (options) => {
    const elements = document.querySelectorAll(options.targets);
    elements.forEach(el => {
      Object.keys(options).forEach(prop => {
        if (prop !== 'targets' && prop !== 'duration' && prop !== 'easing') {
          if (el.style) el.style[prop] = Array.isArray(options[prop]) ? options[prop][1] : options[prop];
        }
      });
    });
  };

  initializeApp();
};

// Wait for DOM and try to initialize
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(initializeLibraries, 500);
  });
} else {
  setTimeout(initializeLibraries, 500);
}

// Fallback timer - always ensure initialization
setTimeout(() => {
  if (!window.portfolioInitialized) {
    console.log('🔄 Initializing with fallback for maximum compatibility');
    initializeFallback();
  }
}, 2000);

// Main application initialization
const initializeApp = () => {
  console.log('🚀 Initializing Space Phoenix Portfolio');

  // Register GSAP plugins if available
  if (gsap && ScrollTrigger) {
    gsap.registerPlugin(ScrollTrigger);
  }

  // Check for reduced motion preference
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  // Loading state management
  class LoadingManager {
    constructor() {
      this.loadingIndicator = document.getElementById('loading-indicator');
      this.loadedSections = new Set();
      this.totalSections = document.querySelectorAll('section').length;
      this.init();
    }

    init() {
      // Hide loading indicator after initial load
      window.addEventListener('load', () => {
        setTimeout(() => this.hideLoading(), 500);
      });

      // Force hide loading after maximum wait time
      setTimeout(() => {
        if (this.loadingIndicator && this.loadingIndicator.style.display !== 'none') {
          console.log('🔄 Force hiding loading screen after timeout');
          this.hideLoading();
        }
      }, 3000);

      // Progressive loading of sections
      this.setupIntersectionObserver();
    }

    hideLoading() {
      if (this.loadingIndicator) {
        gsap.to(this.loadingIndicator, {
          opacity: 0,
          duration: 0.5,
          onComplete: () => {
            this.loadingIndicator.style.display = 'none';
            this.revealContent();
          }
        });
      }
    }

    revealContent() {
      // Reveal sections progressively
      const sections = document.querySelectorAll('.loading');
      sections.forEach((section, index) => {
        gsap.to(section, {
          opacity: 1,
          y: 0,
          duration: 0.8,
          delay: index * 0.1,
          ease: 'power3.out',
          onComplete: () => {
            section.classList.remove('loading');
            section.classList.add('loaded');
          }
        });
      });
    }

    setupIntersectionObserver() {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !this.loadedSections.has(entry.target)) {
            this.loadedSections.add(entry.target);
            entry.target.classList.add('loaded');

            // Trigger section-specific animations
            this.animateSection(entry.target);
          }
        });
      }, { threshold: 0.1 });

      document.querySelectorAll('section').forEach(section => {
        observer.observe(section);
      });
    }

    animateSection(section) {
      if (prefersReducedMotion) return;

      const elements = section.querySelectorAll('h2, h3, p, .card, .badge, .btn');
      if (gsap && gsap.from) {
        gsap.from(elements, {
          y: 20,
          opacity: 0,
          duration: 0.6,
          stagger: 0.05,
          ease: 'power3.out'
        });
      } else {
        // Fallback: just make elements visible
        elements.forEach(el => {
          el.style.opacity = '1';
          el.style.transform = 'translateY(0)';
        });
      }
    }
  }

  const loadingManager = new LoadingManager();

  // Error handling for failed animations
  window.addEventListener('error', (e) => {
    console.warn('Animation error caught:', e.error);
    // Fallback to basic functionality
    document.querySelectorAll('.loading').forEach(el => {
      el.classList.remove('loading');
      el.classList.add('loaded');
      el.style.opacity = '1';
      el.style.transform = 'translateY(0)';
    });
  });

  // Ensure all elements are visible after a timeout
  setTimeout(() => {
    document.querySelectorAll('.loading').forEach(el => {
      el.classList.remove('loading');
      el.classList.add('loaded');
      el.style.opacity = '1';
      el.style.transform = 'translateY(0)';
    });
  }, 3000);

  // Emergency fallback - force hide loading screen
  setTimeout(() => {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
      console.log('🚨 Emergency: Force hiding loading screen');
      loadingIndicator.style.display = 'none';
      document.body.style.overflow = '';

      // Make sure all content is visible
      document.querySelectorAll('.loading').forEach(el => {
        el.classList.remove('loading');
        el.classList.add('loaded');
        el.style.opacity = '1';
        el.style.transform = 'translateY(0)';
      });
    }
  }, 5000);

  // Enhanced anchor links with smooth transitions
  document.querySelectorAll('a[href^="#"]').forEach(a => a.addEventListener('click', e => {
    const href = a.getAttribute('href');
    if (href.length > 1) {
      e.preventDefault();
      const target = document.querySelector(href);
      if (target) {
        // Add visual feedback
        if (gsap && gsap.to) {
          gsap.to(a, { scale: 0.95, duration: 0.1, yoyo: true, repeat: 1 });
        }

        // Use Lenis for buttery smooth scrolling
        if (lenis && typeof lenis.scrollTo === 'function') {
          lenis.scrollTo(target, {
            offset: -80,
            duration: 1.2,
            easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t))
          });
        } else {
          // Fallback smooth scroll
          target.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    }
  }));

  // Terminal animation
  const terminalText = document.getElementById('terminalText');
  if (terminalText) {
    const commands = [
      '$ git clone https://github.com/apoorv-deep/portfolio',
      '✓ Cloning repository...',
      '✓ Setting up Django environment...',
      '✓ Configuring MySQL database...',
      '✓ Installing dependencies...',
      '',
      '🚀 Application deployed successfully!',
      '📊 ERP System: Online',
      '⚡ CMS Platform: Running',
      '',
      '$ python manage.py runserver',
      'Development server is running at http://127.0.0.1:8000/',
      '',
      '$ apoorv --status',
      'Status: READY TO CODE 💻'
    ];

    let i = 0;
    const typeWriter = () => {
      if (i < commands.length) {
        terminalText.textContent += commands[i] + '\n';
        i++;
        setTimeout(typeWriter, 800);
      }
    };

    // Start terminal animation when about section is visible
    const aboutSection = document.getElementById('about');
    if (aboutSection) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setTimeout(typeWriter, 1000);
            observer.unobserve(entry.target);
          }
        });
      }, { threshold: 0.3 });
      observer.observe(aboutSection);
    }
  }

  // Set current year in footer
  const yearElement = document.getElementById('year');
  if (yearElement) {
    yearElement.textContent = new Date().getFullYear();
  }

  // Initialize enhanced interactions
  initializeEnhancedInteractions();

  console.log('✅ Portfolio initialization complete');
};

// Enhanced Interactions System
function initializeEnhancedInteractions() {
  console.log('🎯 Initializing enhanced interactions');

  // Custom Cursor System
  class CustomCursor {
    constructor() {
      this.cursor = document.getElementById('customCursor');
      this.follower = document.getElementById('customCursorFollower');
      this.isSupported = !('ontouchstart' in window) && window.innerWidth > 768;

      if (this.isSupported && this.cursor && this.follower) {
        this.init();
      }
    }

    init() {
      document.body.classList.add('custom-cursor-enabled');

      // Mouse move handler
      document.addEventListener('mousemove', (e) => {
        const x = e.clientX;
        const y = e.clientY;

        // Update cursor position immediately
        this.cursor.style.left = x + 'px';
        this.cursor.style.top = y + 'px';

        // Update follower with slight delay
        setTimeout(() => {
          this.follower.style.left = x + 'px';
          this.follower.style.top = y + 'px';
        }, 50);
      });

      // Hover effects for interactive elements
      const interactiveElements = document.querySelectorAll('a, button, .btn, .card, input, textarea');

      interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
          document.body.classList.add('cursor-hover');
        });

        element.addEventListener('mouseleave', () => {
          document.body.classList.remove('cursor-hover');
        });
      });

      // Text selection cursor
      const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span');

      textElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
          document.body.classList.add('cursor-text');
        });

        element.addEventListener('mouseleave', () => {
          document.body.classList.remove('cursor-text');
        });
      });
    }
  }

  // Scroll Progress Indicator
  class ScrollProgress {
    constructor() {
      this.progressBar = document.getElementById('scrollProgress');
      if (this.progressBar) {
        this.init();
      }
    }

    init() {
      window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;

        this.progressBar.style.width = Math.min(scrollPercent, 100) + '%';
      });
    }
  }

  // Mouse Parallax System
  class MouseParallax {
    constructor() {
      this.elements = [];
      this.mouseX = 0;
      this.mouseY = 0;
      this.isSupported = !('ontouchstart' in window);

      if (this.isSupported) {
        this.init();
      }
    }

    init() {
      // Register parallax elements
      const phoenixSvg = document.getElementById('phoenix');
      const phoenixWrap = document.querySelector('.phoenix-wrap');
      const cards = document.querySelectorAll('.card');

      if (phoenixSvg) {
        this.elements.push({ element: phoenixSvg, intensity: 0.02, type: 'phoenix' });
      }

      if (phoenixWrap) {
        this.elements.push({ element: phoenixWrap, intensity: 0.01, type: 'container' });
      }

      cards.forEach(card => {
        this.elements.push({ element: card, intensity: 0.005, type: 'card' });
      });

      // Mouse move handler
      document.addEventListener('mousemove', (e) => {
        this.mouseX = (e.clientX - window.innerWidth / 2) / window.innerWidth;
        this.mouseY = (e.clientY - window.innerHeight / 2) / window.innerHeight;

        this.updateParallax();
      });
    }

    updateParallax() {
      this.elements.forEach(({ element, intensity, type }) => {
        const moveX = this.mouseX * intensity * 100;
        const moveY = this.mouseY * intensity * 100;

        if (type === 'phoenix') {
          element.style.transform = `translate(${moveX}px, ${moveY}px) rotate(${moveX * 0.1}deg)`;
        } else if (type === 'card') {
          element.style.transform = `translate(${moveX}px, ${moveY}px)`;
        } else {
          element.style.transform = `translate(${moveX}px, ${moveY}px)`;
        }
      });
    }
  }

  // Scroll-Triggered Animations
  class ScrollAnimations {
    constructor() {
      this.elements = [];
      this.init();
    }

    init() {
      // Register elements for scroll animations
      const sections = document.querySelectorAll('section');
      const cards = document.querySelectorAll('.card');
      const badges = document.querySelectorAll('.badge');

      sections.forEach(section => {
        this.elements.push({ element: section, type: 'section' });
      });

      cards.forEach(card => {
        this.elements.push({ element: card, type: 'card' });
      });

      badges.forEach(badge => {
        this.elements.push({ element: badge, type: 'badge' });
      });

      // Enhanced intersection observer
      this.observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.animateElement(entry.target);
          }
        });
      }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      });

      this.elements.forEach(({ element }) => {
        this.observer.observe(element);
      });
    }

    animateElement(element) {
      if (prefersReducedMotion) {
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
        return;
      }

      if (gsap && gsap.from) {
        const elementType = element.classList.contains('card') ? 'card' :
                           element.classList.contains('badge') ? 'badge' : 'section';

        switch (elementType) {
          case 'card':
            gsap.from(element, {
              y: 50,
              opacity: 0,
              duration: 0.8,
              ease: 'power3.out',
              delay: Math.random() * 0.2
            });
            break;
          case 'badge':
            gsap.from(element, {
              scale: 0.8,
              opacity: 0,
              duration: 0.5,
              ease: 'back.out(1.7)',
              delay: Math.random() * 0.1
            });
            break;
          default:
            gsap.from(element.children, {
              y: 30,
              opacity: 0,
              duration: 0.6,
              stagger: 0.1,
              ease: 'power3.out'
            });
        }
      }
    }
  }

  // Phoenix SVG Scroll Effects
  class PhoenixScrollEffects {
    constructor() {
      this.phoenix = document.getElementById('phoenix');
      this.phoenixWrap = document.querySelector('.phoenix-wrap');
      this.scrollY = 0;

      if (this.phoenix && this.phoenixWrap) {
        this.init();
      }
    }

    init() {
      window.addEventListener('scroll', () => {
        this.scrollY = window.pageYOffset;
        this.updatePhoenix();
      });
    }

    updatePhoenix() {
      if (prefersReducedMotion) return;

      const scrollProgress = this.scrollY / (document.body.scrollHeight - window.innerHeight);
      const rotation = scrollProgress * 360;
      const scale = 1 + (scrollProgress * 0.2);
      const opacity = Math.max(0.3, 1 - (scrollProgress * 0.7));

      if (gsap && gsap.set) {
        gsap.set(this.phoenix, {
          rotation: rotation,
          scale: scale,
          opacity: opacity
        });

        // Add floating animation
        gsap.set(this.phoenixWrap, {
          y: Math.sin(this.scrollY * 0.01) * 10
        });
      } else {
        // Fallback without GSAP
        this.phoenix.style.transform = `rotate(${rotation}deg) scale(${scale})`;
        this.phoenix.style.opacity = opacity;
        this.phoenixWrap.style.transform = `translateY(${Math.sin(this.scrollY * 0.01) * 10}px)`;
      }
    }
  }

  // Enhanced Parallax Scrolling
  class EnhancedParallax {
    constructor() {
      this.elements = [];
      this.init();
    }

    init() {
      // Register parallax elements with different speeds
      const starfield = document.getElementById('starfield');
      const spaceDust = document.querySelector('.space-dust');
      const sections = document.querySelectorAll('section');

      if (starfield) {
        this.elements.push({ element: starfield, speed: 0.5, type: 'background' });
      }

      if (spaceDust) {
        this.elements.push({ element: spaceDust, speed: 0.3, type: 'overlay' });
      }

      sections.forEach((section, index) => {
        this.elements.push({
          element: section,
          speed: 0.1 + (index * 0.05),
          type: 'section'
        });
      });

      window.addEventListener('scroll', () => {
        this.updateParallax();
      });
    }

    updateParallax() {
      if (prefersReducedMotion) return;

      const scrollY = window.pageYOffset;

      this.elements.forEach(({ element, speed, type }) => {
        const yPos = -(scrollY * speed);

        if (type === 'background') {
          element.style.transform = `translateY(${yPos}px)`;
        } else if (type === 'section') {
          element.style.transform = `translateY(${yPos * 0.1}px)`;
        }
      });
    }
  }

  // Initialize all systems
  const customCursor = new CustomCursor();
  const scrollProgress = new ScrollProgress();
  const mouseParallax = new MouseParallax();
  const scrollAnimations = new ScrollAnimations();
  const phoenixScrollEffects = new PhoenixScrollEffects();
  const enhancedParallax = new EnhancedParallax();
}
